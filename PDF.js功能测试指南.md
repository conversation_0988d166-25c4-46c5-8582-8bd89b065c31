# PDF.js 功能测试指南

## 测试准备

### 1. 确保依赖已安装
```bash
cd frontend
npm install pdfjs-dist
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 访问测试页面
```
http://localhost:3000/#/wel/subvenues
```

## 测试场景

### 场景1：基本 PDF 预览功能

**测试步骤：**
1. 在分会场列表中找到任意分会场
2. 点击"查看议程"按钮
3. 观察 PDF 预览弹窗是否正确打开
4. 验证 PDF 内容是否正确显示

**预期结果：**
- 弹窗正确打开
- 显示加载状态
- PDF 内容在 Canvas 中正确渲染
- 页面信息正确显示（1 / 总页数）

### 场景2：页面导航功能

**测试步骤：**
1. 打开一个多页 PDF 文档
2. 点击"下一页"按钮
3. 验证页面是否切换到第2页
4. 点击"上一页"按钮
5. 验证页面是否回到第1页
6. 测试按钮的启用/禁用状态

**预期结果：**
- 页面正确切换
- 页面计数器正确更新
- 第1页时"上一页"按钮禁用
- 最后一页时"下一页"按钮禁用

### 场景3：下载功能

**测试步骤：**
1. 打开 PDF 预览
2. 点击"下载"按钮
3. 验证是否触发文件下载

**预期结果：**
- 在新标签页中打开 PDF 文件
- 或者触发文件下载（取决于浏览器设置）

### 场景4：关闭功能

**测试步骤：**
1. 打开 PDF 预览
2. 点击关闭按钮（X）
3. 验证弹窗是否关闭
4. 重新打开预览
5. 点击遮罩层（弹窗外部区域）
6. 验证弹窗是否关闭

**预期结果：**
- 两种方式都能正确关闭弹窗
- 关闭后页面恢复正常状态

### 场景5：错误处理

**测试步骤：**
1. 修改测试数据，使用无效的 PDF URL
2. 点击"查看议程"按钮
3. 观察错误处理是否正确

**预期结果：**
- 显示友好的错误提示
- 提供下载按钮作为备选方案
- 不会导致页面崩溃

### 场景6：加载状态

**测试步骤：**
1. 使用较大的 PDF 文件或较慢的网络
2. 点击"查看议程"按钮
3. 观察加载状态显示

**预期结果：**
- 显示加载动画和提示文字
- 加载完成后自动隐藏加载状态
- 显示 PDF 内容

## 移动端测试

### 1. 响应式布局测试
- 使用浏览器开发者工具切换到移动设备模式
- 测试不同屏幕尺寸下的显示效果
- 验证按钮和文字是否适配移动端

### 2. 触摸操作测试
- 测试触摸点击按钮的响应性
- 验证滚动操作是否正常
- 测试双击和长按行为

## 浏览器兼容性测试

### 推荐测试的浏览器：
1. **Chrome**（最新版本）
2. **Firefox**（最新版本）
3. **Safari**（如果使用 Mac）
4. **Edge**（最新版本）

### 测试要点：
- PDF 渲染质量
- 导航功能响应性
- 错误处理一致性
- 性能表现

## 性能测试

### 1. 大文件测试
- 测试 5MB+ 的 PDF 文件
- 观察加载时间和内存使用
- 验证页面切换的流畅性

### 2. 多页文档测试
- 测试 50+ 页的 PDF 文档
- 验证导航功能的性能
- 检查内存是否有泄漏

### 3. 并发测试
- 同时打开多个 PDF 预览
- 验证系统稳定性
- 检查资源使用情况

## 常见问题排查

### 1. PDF 无法显示
**可能原因：**
- PDF URL 无效
- 网络连接问题
- PDF 文件损坏
- 浏览器不支持

**排查步骤：**
1. 检查浏览器控制台错误信息
2. 验证 PDF URL 是否可以直接访问
3. 尝试不同的浏览器
4. 检查网络连接

### 2. 导航按钮不工作
**可能原因：**
- JavaScript 错误
- 事件绑定失败
- PDF 文档只有一页

**排查步骤：**
1. 检查控制台错误
2. 验证 PDF 文档页数
3. 测试按钮点击事件

### 3. 渲染质量问题
**可能原因：**
- 缩放比例不合适
- Canvas 尺寸问题
- PDF 文档质量

**排查步骤：**
1. 调整缩放比例
2. 检查 Canvas 尺寸设置
3. 尝试不同的 PDF 文档

### 4. 性能问题
**可能原因：**
- PDF 文件过大
- 内存泄漏
- 浏览器性能限制

**排查步骤：**
1. 监控内存使用
2. 测试较小的文件
3. 检查是否有内存泄漏

## 测试数据

### 推荐的测试 PDF 文件：
1. **小文件**：1-2页，< 1MB
2. **中等文件**：10-20页，1-5MB
3. **大文件**：50+页，> 5MB
4. **特殊格式**：包含图片、表格、复杂布局的 PDF

### 当前测试数据：
```javascript
// 分会场 E 的 PDF URL
pdfUrl: 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf'
```

## 测试报告模板

### 测试环境：
- 浏览器：
- 版本：
- 操作系统：
- 屏幕分辨率：

### 测试结果：
- [ ] 基本预览功能
- [ ] 页面导航功能
- [ ] 下载功能
- [ ] 关闭功能
- [ ] 错误处理
- [ ] 移动端适配
- [ ] 性能表现

### 发现的问题：
1. 问题描述
2. 重现步骤
3. 预期结果
4. 实际结果
5. 严重程度

### 建议改进：
1. 功能改进建议
2. 性能优化建议
3. 用户体验改进建议
