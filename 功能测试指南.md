# 分会场视频播放和PDF预览功能测试指南

## 测试准备

1. 启动前端开发服务器：
   ```bash
   cd frontend
   npm run dev
   ```

2. 访问分会场页面：
   ```
   http://localhost:3000/#/wel/subvenues
   ```

## 测试场景

### 场景1：测试视频播放功能

**测试步骤：**
1. 在分会场列表中找到状态为"已结束"的分会场（如"技术创新沙龙"）
2. 点击"观看回放"按钮
3. 验证是否弹出视频播放器
4. 验证视频是否能正常播放
5. 点击关闭按钮或遮罩层，验证播放器是否正确关闭

**预期结果：**
- 弹出视频播放弹窗
- 显示会场名称和"视频播放"标题
- 视频自动开始播放
- 播放控件正常工作（播放/暂停、音量、进度条等）
- 能够正常关闭播放器

### 场景2：测试PDF预览功能

**测试步骤：**
1. 在任意分会场卡片中点击"查看议程"按钮
2. 验证是否弹出PDF预览器
3. 验证PDF文档是否正确显示
4. 点击"下载"按钮，验证是否能下载PDF
5. 点击关闭按钮或遮罩层，验证预览器是否正确关闭

**预期结果：**
- 弹出PDF预览弹窗
- 显示会场名称和"议程预览"标题
- PDF文档在iframe中正确显示
- 下载按钮功能正常
- 能够正常关闭预览器

### 场景3：测试查看详情功能

**测试步骤：**
1. 找到状态为"准备中"或其他状态的分会场
2. 点击主操作按钮（"查看详情"等）
3. 验证是否触发视频播放功能

**预期结果：**
- 如果有视频URL，应该打开视频播放器
- 如果没有视频URL，应该显示"暂无视频内容"提示

### 场景4：测试错误处理

**测试步骤：**
1. 修改测试数据，将某个分会场的videoUrl设为空字符串
2. 点击该分会场的视频相关按钮
3. 验证错误提示是否正确显示

**预期结果：**
- 显示友好的错误提示信息
- 不会导致页面崩溃或JavaScript错误

## 移动端测试

1. 使用浏览器的开发者工具切换到移动设备模式
2. 重复上述所有测试场景
3. 验证弹窗在移动设备上的显示效果
4. 验证触摸操作的响应性

## 浏览器兼容性测试

建议在以下浏览器中测试：
- Chrome（最新版本）
- Firefox（最新版本）
- Safari（如果使用Mac）
- Edge（最新版本）

## 测试数据说明

当前使用的测试数据包含：

**分会场D - 产业发展论坛：**
- 视频URL：https://www.w3schools.com/html/mov_bbb.mp4
- PDF URL：https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf

**分会场E - 技术创新沙龙：**
- 视频URL：https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4
- PDF URL：https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf

## 常见问题排查

### 视频无法播放
1. 检查视频URL是否有效
2. 检查网络连接
3. 验证浏览器是否支持视频格式
4. 检查浏览器控制台是否有错误信息

### PDF无法显示
1. 检查PDF URL是否有效
2. 验证PDF文件是否损坏
3. 检查浏览器是否支持PDF预览
4. 尝试直接在新标签页中打开PDF URL

### 弹窗样式异常
1. 检查CSS样式是否正确加载
2. 验证是否有样式冲突
3. 检查浏览器开发者工具中的样式规则

## 后端集成测试

当后端API准备就绪时：
1. 确保后端返回的数据包含videoUrl和pdfUrl字段
2. 测试真实的视频和PDF文件
3. 验证文件上传和存储功能
4. 测试不同格式的视频和PDF文件

## 性能测试

1. 测试大文件的加载性能
2. 验证多个弹窗同时打开的情况
3. 检查内存使用情况
4. 测试网络较慢时的用户体验
