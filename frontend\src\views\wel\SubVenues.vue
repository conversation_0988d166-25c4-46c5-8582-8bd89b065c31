<template>
  <div class="page-container">
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-video"></i>
          <h2>{{ mainTitle }}</h2>
          <p>{{subTitle}}</p>
        </div>

        <!-- 分会场列表 -->
        <div class="venue-list">

          <!-- 动态渲染其他分会场 -->
          <div v-for="venue in otherVenues" :key="venue.id" class="venue-item">
            <div class="venue-header">
              <div class="venue-number">{{ venue.number }}</div>
              <div class="venue-info">
                <h3>{{ venue.name }}</h3>
                <div class="venue-meta">
                  <span class="venue-location">
                    <i class="fas fa-map-marker-alt"></i>
                    {{ venue.location }}
                  </span>
                  <span class="venue-time">
                    <i class="fas fa-clock"></i>
                    {{ venue.time }}
                  </span>
                </div>
              </div>
              <div class="venue-status" :class="venue.status">
                <i :class="getStatusIcon(venue.status)"></i>
                {{ getStatusText(venue.status) }}
              </div>
            </div>

            <div class="venue-content">
              <div class="session-info">
                <h4>{{ venue.currentTopic }}</h4>
                <div class="speaker-info">
                  <div class="speaker-avatar">
                    <i class="fas fa-user"></i>
                  </div>
                  <div class="speaker-details">
                    <span class="speaker-name">{{ venue.speaker }}</span>
                    <span class="speaker-title">{{ venue.speakerTitle }}</span>
                  </div>
                </div>
              </div>

              <div class="venue-stats-row">
                <div class="stat-item">
                  <i class="fas fa-users"></i>
                  <span>{{ venue.currentAttendees }}/{{ venue.capacity }}人</span>
                </div>
                <div class="stat-item">
                  <i :class="getStreamIcon(venue.status)"></i>
                  <span>{{ getStreamStatus(venue.status) }}</span>
                </div>
              </div>
            </div>

            <div class="venue-actions">
              <button
                class="action-btn"
                :class="getActionButtonClass(venue.status)"
                @click="handleVenueAction(venue)"
                :disabled="venue.status === 'preparing'"
              >
                <i :class="getActionIcon(venue.status)"></i>
                {{ getActionText(venue.status) }}
              </button>
              <button class="action-btn secondary" @click="viewSchedule(venue.number)">
                <i class="fas fa-calendar"></i>
                查看议程
              </button>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="venue-stats">
          <div class="stat-item">
            <i class="fas fa-building"></i>
            <span>总会场数：<strong>{{ totalVenues }}</strong></span>
          </div>
          <div class="stat-item">
            <i class="fas fa-users"></i>
            <span>参与人数：<strong>{{ totalCapacity }}</strong>人</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-database"></i>
            <span>数据源：<strong>{{ dataSource === 'api' ? 'API' : dataSource === 'fallback' ? '默认' : '未知' }}</strong></span>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import { getList } from '@/api/subvenue/subVenue';
import { dataTransformers } from '@/utils/apiHelper';
import { getDictionary } from '@/api/system/dictbiz'

export default {
  name: 'SubVenues',
  data() {
    return {
      mainTitle:'',
      subTitle:'',
      venues: [],
      otherVenues: [], // 动态渲染的其他分会场
      dataSource: 'unknown',
      responseTime: 0,
      defaultVenuesData: [
        {
          id: 4,
          number: 'D',
          name: '产业发展论坛',
          location: '会议中心D厅',
          time: '15:00-18:00',
          capacity: 80,
          currentAttendees: 65,
          status: 'preparing',
          currentTopic: '产业升级与发展趋势',
          speaker: '陈专家',
          speakerTitle: '产业发展顾问',
          videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4', // 示例视频URL
          pdfUrl: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' // 示例PDF URL
        },
        {
          id: 5,
          number: 'E',
          name: '技术创新沙龙',
          location: '会议中心E厅',
          time: '10:00-12:00',
          capacity: 60,
          currentAttendees: 45,
          status: 'finished',
          currentTopic: '新技术应用与实践',
          speaker: '刘工程师',
          speakerTitle: '技术创新专家',
          videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4', // 示例视频URL
          pdfUrl: 'https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf' // 示例PDF URL
        }
      ]
    }
  },
  computed: {
    totalVenues() {
      return 3 + this.otherVenues.length; // 3个固定分会场 + 动态分会场
    },
    totalCapacity() {
      const fixedCapacity = 150 + 100 + 120; // A厅150 + B厅100 + C厅120
      const dynamicCapacity = this.otherVenues.reduce((sum, venue) => sum + venue.capacity, 0);
      return fixedCapacity + dynamicCapacity;
    }
  },
  async mounted() {
    // 首先设置默认数据，确保页面有内容显示
    this.otherVenues = [...this.defaultVenuesData];
    console.log('初始化默认分会场数据:', this.otherVenues);

    // 然后尝试加载API数据
    await this.loadVenuesData();
    await this.loadData();
  },
  methods: {
    async loadData() {
      const response = await getDictionary({
          code: 'hy_sub_venue' // 字典编码，需要在后台配置
        });
        // 检查响应格式
        if (response && response.data && response.data.success) {
          const dictData = response.data.data;
          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            // 从字典数据中提取文本
            this.mainTitle = dictData.find(item => item.dictValue === '主标题').dictKey;
            this.subTitle= dictData.find(item => item.dictValue === '副标题').dictKey;
          } else {
            console.log('API返回数据为空');
          }
        } else {
          throw new Error('API响应格式不正确');
        }
    },
    /**
     * 加载分会场数据
     */
    async loadVenuesData() {
      const startTime = Date.now();

      try {
        console.log('开始加载分会场数据...');

        // 直接调用API
        const response = await getList(1, 20, {});
        console.log('分会场API响应:', response);

        // 检查响应格式
        if (response && response.data && response.data.success) {
          // 使用数据转换器处理数据
          const transformedData = dataTransformers.subVenues(response.data);
          console.log('转换后的分会场数据:', transformedData);

          if (transformedData && transformedData.length > 0) {
            // 将API数据赋值给otherVenues，用于动态渲染
            this.otherVenues = transformedData;
            console.log('API数据已加载到otherVenues:', this.otherVenues);
            this.dataSource = 'api';
          } else {
            console.log('API返回数据为空，保持默认数据');
            this.dataSource = 'fallback';
          }
          this.hasError = false;
          this.errorMessage = '';
        } else {
          throw new Error('API响应格式不正确');
        }

        this.responseTime = Date.now() - startTime;

      } catch (error) {
        console.error('加载分会场数据失败:', error);
        // 保持已有的默认数据，不要覆盖
        if (this.otherVenues.length === 0) {
          this.otherVenues = [...this.defaultVenuesData];
        }
        this.dataSource = 'fallback';
        this.hasError = true;
        this.errorMessage = error.message || '数据加载失败，使用默认数据';
        this.responseTime = Date.now() - startTime;

        console.log('API调用失败，保持默认数据:', this.otherVenues);
      }
    },

    getStatusText(status) {
      const statusMap = {
        'active': '进行中',
        'preparing': '准备中',
        'finished': '已结束',
        'cancelled': '已取消'
      };
      return statusMap[status] || '未知';
    },

    getStatusIcon(status) {
      const iconMap = {
        'active': 'fas fa-circle',
        'preparing': 'fas fa-circle',
        'finished': 'fas fa-check-circle',
        'cancelled': 'fas fa-times-circle'
      };
      return iconMap[status] || 'fas fa-circle';
    },

    getStreamIcon(status) {
      const iconMap = {
        'active': 'fas fa-video',
        'preparing': 'fas fa-pause',
        'finished': 'fas fa-check',
        'cancelled': 'fas fa-times'
      };
      return iconMap[status] || 'fas fa-pause';
    },

    getStreamStatus(status) {
      const statusMap = {
        'active': '直播中',
        'preparing': '待开始',
        'finished': '已完成',
        'cancelled': '已取消'
      };
      return statusMap[status] || '未知';
    },

    getActionButtonClass(status) {
      const classMap = {
        'active': 'primary',
        'preparing': 'disabled',
        'finished': 'secondary',
        'cancelled': 'disabled'
      };
      return classMap[status] || 'secondary';
    },

    getActionIcon(status) {
      const iconMap = {
        'active': 'fas fa-sign-in-alt',
        'preparing': 'fas fa-clock',
        'finished': 'fas fa-play',
        'cancelled': 'fas fa-times'
      };
      return iconMap[status] || 'fas fa-info';
    },

    getActionText(status) {
      const textMap = {
        'active': '进入会场',
        'preparing': '未开始',
        'finished': '观看回放',
        'cancelled': '已取消'
      };
      return textMap[status] || '查看详情';
    },

    /**
     * 进入分会场
     */
    joinVenue(venueNumber) {
      if (this.$message) {
        this.$message.success(`正在进入分会场${venueNumber}`);
      } else {
        alert(`正在进入分会场${venueNumber}\n（演示功能）`);
      }
    },

    /**
     * 查看议程 - PDF预览
     */
    viewSchedule(venueNumber) {
      const venue = this.otherVenues.find(v => v.number === venueNumber);
      if (venue && venue.pdfUrl) {
        // 打开PDF预览
        this.openPdfPreview(venue.pdfUrl, venue.name);
      } else {
        if (this.$message) {
          this.$message.warning(`分会场${venueNumber}暂无议程文档`);
        } else {
          alert(`分会场${venueNumber}暂无议程文档`);
        }
      }
    },

    /**
     * 观看回放 - 视频播放
     */
    viewReplay(venueNumber) {
      const venue = this.otherVenues.find(v => v.number === venueNumber);
      if (venue && venue.videoUrl) {
        // 打开视频播放
        this.openVideoPlayer(venue.videoUrl, venue.name);
      } else {
        if (this.$message) {
          this.$message.warning(`分会场${venueNumber}暂无回放视频`);
        } else {
          alert(`分会场${venueNumber}暂无回放视频`);
        }
      }
    },

    /**
     * 处理分会场操作
     */
    handleVenueAction(venue) {
      switch (venue.status) {
        case 'active':
          this.joinVenue(venue.number);
          break;
        case 'finished':
          this.viewReplay(venue.number);
          break;
        case 'preparing':
          // 准备中不允许操作
          break;
        default:
          // 默认情况下查看详情（视频播放）
          if (venue.videoUrl) {
            this.openVideoPlayer(venue.videoUrl, venue.name);
          } else {
            if (this.$message) {
              this.$message.info(`分会场${venue.number}暂无视频内容`);
            } else {
              alert(`分会场${venue.number}暂无视频内容`);
            }
          }
      }
    },

    /**
     * 打开视频播放器
     */
    openVideoPlayer(videoUrl, venueName) {
      // 创建视频播放弹窗
      const videoModal = document.createElement('div');
      videoModal.className = 'video-modal';
      videoModal.innerHTML = `
        <div class="video-modal-overlay" onclick="this.parentElement.remove()">
          <div class="video-modal-content" onclick="event.stopPropagation()">
            <div class="video-modal-header">
              <h3>${venueName} - 视频播放</h3>
              <button class="video-modal-close" onclick="this.closest('.video-modal').remove()">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <div class="video-modal-body">
              <video controls autoplay style="width: 100%; height: 400px;">
                <source src="${videoUrl}" type="video/mp4">
                您的浏览器不支持视频播放。
              </video>
            </div>
          </div>
        </div>
      `;

      // 添加样式
      if (!document.querySelector('#video-modal-styles')) {
        const styles = document.createElement('style');
        styles.id = 'video-modal-styles';
        styles.textContent = `
          .video-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
          }
          .video-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .video-modal-content {
            background: white;
            border-radius: 10px;
            max-width: 90%;
            max-height: 90%;
            overflow: hidden;
          }
          .video-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
          }
          .video-modal-header h3 {
            margin: 0;
            color: #333;
          }
          .video-modal-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #666;
          }
          .video-modal-close:hover {
            color: #333;
          }
          .video-modal-body {
            padding: 20px;
          }
        `;
        document.head.appendChild(styles);
      }

      document.body.appendChild(videoModal);
    },

    /**
     * 打开PDF预览
     */
    openPdfPreview(pdfUrl, venueName) {
      // 创建PDF预览弹窗
      const pdfModal = document.createElement('div');
      pdfModal.className = 'pdf-modal';
      pdfModal.innerHTML = `
        <div class="pdf-modal-overlay" onclick="this.parentElement.remove()">
          <div class="pdf-modal-content" onclick="event.stopPropagation()">
            <div class="pdf-modal-header">
              <h3>${venueName} - 议程预览</h3>
              <div class="pdf-modal-actions">
                <button class="pdf-modal-download" onclick="window.open('${pdfUrl}', '_blank')">
                  <i class="fas fa-download"></i> 下载
                </button>
                <button class="pdf-modal-close" onclick="this.closest('.pdf-modal').remove()">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
            <div class="pdf-modal-body">
              <iframe src="${pdfUrl}" style="width: 100%; height: 500px; border: none;"></iframe>
            </div>
          </div>
        </div>
      `;

      // 添加样式
      if (!document.querySelector('#pdf-modal-styles')) {
        const styles = document.createElement('style');
        styles.id = 'pdf-modal-styles';
        styles.textContent = `
          .pdf-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
          }
          .pdf-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .pdf-modal-content {
            background: white;
            border-radius: 10px;
            max-width: 95%;
            max-height: 95%;
            overflow: hidden;
          }
          .pdf-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
          }
          .pdf-modal-header h3 {
            margin: 0;
            color: #333;
          }
          .pdf-modal-actions {
            display: flex;
            gap: 10px;
          }
          .pdf-modal-download,
          .pdf-modal-close {
            background: none;
            border: none;
            font-size: 14px;
            cursor: pointer;
            color: #666;
            padding: 5px 10px;
            border-radius: 5px;
          }
          .pdf-modal-download:hover {
            background: #f0f0f0;
            color: #4682B4;
          }
          .pdf-modal-close:hover {
            background: #f0f0f0;
            color: #333;
          }
          .pdf-modal-body {
            padding: 20px;
          }
        `;
        document.head.appendChild(styles);
      }

      document.body.appendChild(pdfModal);
    }
  }
}
</script>
<style scoped>
/* 页面通用样式 */
.page-container {
    max-width: 375px;
    margin: 0 auto;
    min-height: 100vh;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
}

.page-content {
    margin-top: 20px;
    min-height: calc(100vh - 80px);
}

.list-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    animation: slideInUp 0.6s ease forwards;
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-header i {
    font-size: 48px;
    color: #4682B4;
    margin-bottom: 15px;
}

.form-header h2 {
    color: #333;
    font-size: 24px;
    margin-bottom: 10px;
}

.form-header p {
    color: #666;
    font-size: 14px;
}

/* 分会场列表样式 */
.venue-list {
    margin: 20px 0;
}

.venue-item {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #4682B4;
    transition: all 0.3s ease;
}

.venue-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
}

/* 分会场头部样式 */
.venue-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.venue-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4682B4, #1E90FF);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    font-weight: bold;
    margin-right: 15px;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
}

.venue-info {
    flex: 1;
}

.venue-info h3 {
    color: #333;
    font-size: 18px;
    margin-bottom: 8px;
    font-weight: 600;
}

.venue-meta {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.venue-location,
.venue-time {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-size: 13px;
}

.venue-location i,
.venue-time i {
    color: #4682B4;
    font-size: 12px;
}

/* 状态标识样式 */
.venue-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.venue-status.active {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.venue-status.preparing {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.venue-status.finished {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.venue-status.cancelled {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.venue-status i {
    font-size: 10px;
}

/* 分会场内容区域样式 */
.venue-content {
    margin-bottom: 20px;
}

.session-info {
    margin-bottom: 15px;
}

.session-info h4 {
    color: #333;
    font-size: 16px;
    margin-bottom: 10px;
    font-weight: 600;
}

.speaker-info {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(70, 130, 180, 0.05);
    padding: 12px;
    border-radius: 10px;
}

.speaker-avatar {
    width: 40px;
    height: 40px;
    background: rgba(70, 130, 180, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.speaker-avatar i {
    font-size: 18px;
    color: #4682B4;
}

.speaker-details {
    flex: 1;
}

.speaker-name {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    display: block;
    margin-bottom: 2px;
}

.speaker-title {
    color: #666;
    font-size: 12px;
}

/* 统计信息行样式 */
.venue-stats-row {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
    font-size: 13px;
}

.stat-item i {
    color: #4682B4;
    font-size: 14px;
}

/* 操作按钮样式 */
.venue-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.action-btn {
    background: #4682B4;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
    flex: 1;
    justify-content: center;
}

.action-btn.primary {
    background: linear-gradient(135deg, #4682B4, #1E90FF);
}

.action-btn.secondary {
    background: rgba(70, 130, 180, 0.1);
    color: #4682B4;
    border: 1px solid rgba(70, 130, 180, 0.3);
}

.action-btn.disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
}

.action-btn:hover:not(.disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
}

.action-btn.secondary:hover {
    background: rgba(70, 130, 180, 0.2);
}

.action-btn i {
    font-size: 10px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  

    .list-container {
        padding: 15px;
    }

    .venue-item {
        padding: 15px;
    }

    .venue-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .venue-number {
        width: 40px;
        height: 40px;
        font-size: 16px;
        margin-right: 0;
    }

    .venue-meta {
        flex-direction: column;
        gap: 8px;
    }

    .venue-stats-row {
        flex-direction: column;
        gap: 10px;
    }

    .venue-actions {
        flex-direction: column;
    }

    .action-btn {
        padding: 10px 16px;
        font-size: 13px;
    }

    .speaker-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* 统计信息样式 */
.venue-stats {
    display: flex;
    justify-content: space-around;
    background: white;
    border-radius: 12px;
    padding: 15px;
    margin-top: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.venue-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
}

.venue-stats .stat-item i {
    color: #4682B4;
    font-size: 16px;
}

.venue-stats .stat-item strong {
    color: #4682B4;
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.venue-item:nth-child(1) { animation-delay: 0.1s; }
.venue-item:nth-child(2) { animation-delay: 0.2s; }
.venue-item:nth-child(3) { animation-delay: 0.3s; }
</style>