# 分会场视频播放和PDF预览功能实现说明

## 功能概述

已成功实现分会场页面的两个核心功能：
1. **查看详情** - 视频播放功能
2. **查看议程** - PDF预览功能

## 实现的功能

### 1. 视频播放功能

**触发方式：**
- 点击"查看详情"按钮（对于已结束的会场显示为"观看回放"）
- 点击主操作按钮（根据会场状态显示不同文本）

**功能特点：**
- 弹窗式视频播放器
- 支持HTML5视频播放控件
- 自动播放功能
- 响应式设计，适配移动端
- 点击遮罩层关闭播放器
- 优雅的UI设计

**实现细节：**
- 动态创建视频播放弹窗
- 使用HTML5 `<video>` 标签
- 支持多种视频格式（MP4等）
- 自动注入CSS样式

### 2. PDF预览功能

**触发方式：**
- 点击"查看议程"按钮

**功能特点：**
- 弹窗式PDF预览器
- 内嵌iframe显示PDF内容
- 提供下载按钮
- 响应式设计
- 点击遮罩层关闭预览器

**实现细节：**
- 动态创建PDF预览弹窗
- 使用iframe嵌入PDF文档
- 提供直接下载链接
- 自动注入CSS样式

## 数据结构更新

### 后端数据字段
后端`SubVenueEntity`已包含：
- `videoUrl` - 视频文件URL
- `pdfUrl` - PDF文档URL

### 前端数据转换
更新了`apiHelper.js`中的`subVenues`数据转换器，添加：
```javascript
videoUrl: item.videoUrl || '',
pdfUrl: item.pdfUrl || '',
```

## 代码修改文件

### 1. frontend/src/utils/apiHelper.js
- 在`subVenues`数据转换器中添加了`videoUrl`和`pdfUrl`字段映射

### 2. frontend/src/views/wel/SubVenues.vue
- 更新了`viewSchedule()`方法实现PDF预览
- 更新了`viewReplay()`方法实现视频播放
- 更新了`handleVenueAction()`方法处理不同状态的操作
- 添加了`openVideoPlayer()`方法创建视频播放弹窗
- 添加了`openPdfPreview()`方法创建PDF预览弹窗
- 更新了默认测试数据，包含示例视频和PDF URL

## 使用说明

### 对于开发者
1. 确保后端返回的分会场数据包含`videoUrl`和`pdfUrl`字段
2. 视频文件建议使用MP4格式以确保最佳兼容性
3. PDF文件应该是可以直接在浏览器中预览的格式

### 对于用户
1. **观看视频：**
   - 点击"查看详情"或"观看回放"按钮
   - 在弹出的播放器中观看视频
   - 点击播放器外部区域或关闭按钮退出

2. **查看议程：**
   - 点击"查看议程"按钮
   - 在弹出的预览器中查看PDF文档
   - 可以点击下载按钮下载PDF文件
   - 点击预览器外部区域或关闭按钮退出

## 错误处理

- 当视频URL为空时，显示"暂无视频内容"提示
- 当PDF URL为空时，显示"暂无议程文档"提示
- 支持优雅的错误提示，不会导致页面崩溃

## 测试数据

为了便于测试，已在默认数据中添加了示例URL：
- 视频：使用了公开的测试视频URL
- PDF：使用了公开的测试PDF文档URL

## 浏览器兼容性

- 支持所有现代浏览器
- 移动端友好的响应式设计
- 使用标准HTML5技术，无需额外插件

## 后续优化建议

1. 可以考虑集成专业的视频播放器（如Video.js）
2. 可以添加PDF.js来提供更好的PDF预览体验
3. 可以添加加载状态和进度条
4. 可以添加全屏播放功能
5. 可以添加播放历史记录功能
