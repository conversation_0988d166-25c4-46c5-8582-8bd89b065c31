# PDF.js 实现 PDF 预览功能

## 功能概述

使用 PDF.js 库重新实现了分会场的 PDF 预览功能，提供了更好的用户体验和更强的兼容性。

## 主要特性

### 1. 原生 PDF 渲染
- 使用 PDF.js 在 Canvas 上直接渲染 PDF 内容
- 不依赖浏览器的 PDF 插件
- 支持所有现代浏览器

### 2. 页面导航
- 上一页/下一页按钮
- 页面计数器显示（当前页/总页数）
- 自动禁用不可用的导航按钮

### 3. 用户体验优化
- 加载状态指示器
- 错误处理和友好提示
- 响应式设计
- 下载功能保留

### 4. 性能优化
- 按需渲染页面
- 合适的缩放比例
- 内存管理

## 技术实现

### 1. 依赖安装
```bash
npm install pdfjs-dist
```

### 2. 核心组件

**PDF.js 初始化：**
```javascript
import * as pdfjsLib from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry';

// 配置 worker
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;
```

**PDF 加载和渲染：**
```javascript
async loadPdfWithPdfjs(pdfUrl, modalElement) {
  // 1. 加载PDF文档
  const loadingTask = pdfjsLib.getDocument(pdfUrl);
  const pdfDoc = await loadingTask.promise;
  
  // 2. 渲染页面
  await this.renderPage(pdfDoc, currentPage, canvas, scale);
  
  // 3. 设置导航
  this.updateNavigationButtons(currentPage, pdfDoc.numPages, prevButton, nextButton);
}
```

### 3. 界面组件

**弹窗结构：**
- 标题栏：显示文档名称
- 工具栏：导航按钮、页面信息、下载按钮、关闭按钮
- 内容区：Canvas 渲染区域
- 状态区：加载提示、错误提示

**样式特点：**
- 响应式布局
- 现代化 UI 设计
- 移动端友好
- 高对比度按钮

## 使用方法

### 1. 基本使用
```javascript
// 点击查看议程按钮
viewSchedule(venueNumber) {
  const venue = this.otherVenues.find(v => v.number === venueNumber);
  if (venue && venue.pdfUrl) {
    this.openPdfPreview(venue.pdfUrl, venue.name);
  }
}
```

### 2. 功能操作
- **查看 PDF**：点击"查看议程"按钮
- **翻页**：使用上一页/下一页按钮
- **下载**：点击下载按钮
- **关闭**：点击关闭按钮或点击遮罩层

## 错误处理

### 1. 网络错误
- 显示友好的错误提示
- 提供下载备选方案
- 不会导致页面崩溃

### 2. 文件格式错误
- 检测无效的 PDF 文件
- 显示相应的错误信息
- 引导用户使用其他方式查看

### 3. 浏览器兼容性
- 自动检测浏览器支持情况
- 在不支持的环境中提供降级方案

## 性能优化

### 1. 内存管理
- 只渲染当前页面
- 及时清理不需要的资源
- 避免内存泄漏

### 2. 渲染优化
- 合适的缩放比例（1.5x）
- Canvas 尺寸自适应
- 高质量渲染

### 3. 加载优化
- 异步加载 PDF 文档
- 显示加载进度
- 错误重试机制

## 浏览器兼容性

### 支持的浏览器：
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

### 移动端支持：
- iOS Safari 11+
- Android Chrome 60+
- 其他现代移动浏览器

## 配置选项

### 1. 缩放设置
```javascript
let scale = 1.5; // 可调整缩放比例
```

### 2. 样式定制
- 可以修改 CSS 样式来定制外观
- 支持主题色彩调整
- 响应式断点可配置

## 故障排除

### 1. PDF 无法加载
- 检查 PDF URL 是否有效
- 验证网络连接
- 确认 PDF 文件未损坏

### 2. 渲染问题
- 检查浏览器控制台错误
- 验证 PDF.js 版本兼容性
- 确认 Canvas 支持

### 3. 性能问题
- 检查 PDF 文件大小
- 调整缩放比例
- 优化网络条件

## 后续优化建议

### 1. 功能增强
- 添加缩放控制（放大/缩小）
- 支持全屏模式
- 添加搜索功能
- 支持文本选择和复制

### 2. 性能提升
- 实现页面预加载
- 添加缓存机制
- 支持分块加载大文件

### 3. 用户体验
- 添加键盘快捷键
- 支持触摸手势
- 添加书签功能
- 支持打印功能

## 与旧版本的区别

### 优势：
1. **更好的兼容性**：不依赖浏览器 PDF 插件
2. **统一体验**：所有浏览器中表现一致
3. **更多控制**：可以自定义渲染和交互
4. **更好的错误处理**：友好的错误提示和降级方案

### 注意事项：
1. **文件大小**：PDF.js 会增加约 1-2MB 的包大小
2. **性能**：大型 PDF 文件可能需要更多内存
3. **功能限制**：某些高级 PDF 功能可能不支持
